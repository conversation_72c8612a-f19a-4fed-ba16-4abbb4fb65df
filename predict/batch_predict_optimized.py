#!/usr/bin/env python3
"""
优化的批量股票预测模块

主要优化：
1. 数据预加载和缓存
2. 减少重复计算
3. 智能价格步长
4. 并行优化
5. 内存管理
"""

import sys
import os
import glob
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import time
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor
import threading
import math
from functools import lru_cache

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'hdly'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'kdj_rsi'))

from hdly.hdly_indicator import get_hdly_value
from kdj_rsi.kdj_rsi_indicator import calculate_kdj_rsi


class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, max_size: int = 100):
        self.cache = {}
        self.max_size = max_size
        self.access_order = []
        self.lock = threading.Lock()
    
    def get(self, key: str) -> Optional[pd.DataFrame]:
        """获取缓存数据"""
        with self.lock:
            if key in self.cache:
                # 更新访问顺序
                self.access_order.remove(key)
                self.access_order.append(key)
                return self.cache[key].copy()
            return None
    
    def put(self, key: str, data: pd.DataFrame):
        """存储缓存数据"""
        with self.lock:
            # 如果缓存已满，删除最久未访问的数据
            if len(self.cache) >= self.max_size and key not in self.cache:
                oldest_key = self.access_order.pop(0)
                del self.cache[oldest_key]
            
            self.cache[key] = data.copy()
            if key not in self.access_order:
                self.access_order.append(key)
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_order.clear()


class OptimizedBatchPredictor:
    """优化的批量股票预测器"""
    
    def __init__(self, data_dir: str = "../data", cache_size: int = 100):
        """
        初始化优化的批量预测器
        
        参数:
        data_dir: 数据文件夹路径
        cache_size: 数据缓存大小
        """
        self.data_dir = data_dir
        self.stock_codes = self._get_all_stock_codes()
        self.data_cache = DataCache(cache_size)
        self.results = {}
        self.lock = threading.Lock()
        
        # 预加载常用数据
        self._preload_data()
        
    def _get_all_stock_codes(self) -> List[str]:
        """获取所有股票代码"""
        csv_files = glob.glob(os.path.join(self.data_dir, "*.csv"))
        stock_codes = []
        
        for file_path in csv_files:
            filename = os.path.basename(file_path)
            stock_code = filename.replace('.csv', '')
            if stock_code.isdigit() and len(stock_code) == 6:
                stock_codes.append(stock_code)
        
        return sorted(stock_codes)
    
    def _preload_data(self, max_preload: int = 50):
        """预加载部分数据到缓存"""
        print(f"🔄 预加载前 {max_preload} 只股票数据...")
        
        for i, stock_code in enumerate(self.stock_codes[:max_preload]):
            try:
                data = self._load_stock_data_raw(stock_code)
                if data is not None:
                    self.data_cache.put(stock_code, data)
                    
                if (i + 1) % 10 == 0:
                    print(f"   已预加载: {i + 1}/{max_preload}")
                    
            except Exception as e:
                print(f"   预加载 {stock_code} 失败: {e}")
        
        print(f"✅ 预加载完成")
    
    def _load_stock_data_raw(self, stock_code: str) -> Optional[pd.DataFrame]:
        """原始数据加载（不使用缓存）"""
        try:
            data_paths = [
                f"../data/{stock_code}.csv",
                f"data/{stock_code}.csv",
                f"./data/{stock_code}.csv"
            ]
            
            for path in data_paths:
                if os.path.exists(path):
                    data = pd.read_csv(path)
                    data['date'] = pd.to_datetime(data['date'])
                    data = data.sort_values('date').reset_index(drop=True)
                    return data
            
            return None
            
        except Exception:
            return None
    
    def _load_stock_data(self, stock_code: str) -> Optional[pd.DataFrame]:
        """加载股票数据（使用缓存）"""
        # 先尝试从缓存获取
        data = self.data_cache.get(stock_code)
        if data is not None:
            return data
        
        # 缓存未命中，从文件加载
        data = self._load_stock_data_raw(stock_code)
        if data is not None:
            self.data_cache.put(stock_code, data)
        
        return data
    
    @lru_cache(maxsize=1000)
    def _is_kechuangban(self, stock_code: str) -> bool:
        """判断是否为科创板（缓存结果）"""
        return stock_code.startswith('688')
    
    def _calculate_price_limits(self, prev_close: float, is_kechuang: bool) -> Tuple[float, float]:
        """计算涨跌幅限制"""
        limit_ratio = 0.20 if is_kechuang else 0.10
        min_price = prev_close * (1 - limit_ratio)
        max_price = prev_close * (1 + limit_ratio)
        return min_price, max_price
    
    def _calculate_angle(self, j_prev: float, j_current: float) -> float:
        """计算J值连线角度"""
        if j_current <= j_prev:
            return 0.0
        
        delta_y = j_current - j_prev
        angle_rad = math.atan(delta_y / 1.0)
        return math.degrees(angle_rad)
    
    def _check_j_is_lowest_in_period(self, j_values: List[float], target_index: int, lookback_days: int = 5) -> bool:
        """检查目标J值是否为前N天的最低点"""
        if target_index < lookback_days:
            return False
        
        target_j = j_values[target_index]
        start_index = target_index - lookback_days
        
        for i in range(start_index, target_index):
            if j_values[i] < target_j:
                return False
        
        return True
    
    def _smart_price_search(self, historical_data: pd.DataFrame, target_date: str,
                           prev_close: float, prev_hdly: float, prev_j: float,
                           min_price: float, max_price: float) -> List[Dict]:
        """智能价格搜索 - 使用自适应步长"""
        valid_ranges = []
        
        # 第一阶段：粗略搜索（大步长）
        coarse_step = (max_price - min_price) / 50  # 50个点粗略扫描
        coarse_candidates = []
        
        current_price = min_price
        while current_price <= max_price:
            if self._check_price_conditions(historical_data, target_date, current_price,
                                          prev_hdly, prev_j):
                coarse_candidates.append(current_price)
            current_price += coarse_step
        
        # 第二阶段：精细搜索（小步长）
        if coarse_candidates:
            fine_step = coarse_step / 10  # 精细步长
            
            for candidate in coarse_candidates:
                # 在候选点周围精细搜索
                start_price = max(min_price, candidate - coarse_step)
                end_price = min(max_price, candidate + coarse_step)
                
                fine_price = start_price
                while fine_price <= end_price:
                    result = self._check_price_conditions_detailed(
                        historical_data, target_date, fine_price, prev_hdly, prev_j
                    )
                    if result:
                        valid_ranges.append(result)
                    fine_price += fine_step
        
        return valid_ranges
    
    def _check_price_conditions(self, historical_data: pd.DataFrame, target_date: str,
                               price: float, prev_hdly: float, prev_j: float) -> bool:
        """快速检查价格条件（只返回是否满足）"""
        try:
            # 简化的条件检查
            new_hdly = self._simulate_hdly_fast(historical_data, price)
            if not (new_hdly > 0.1 and new_hdly > prev_hdly):
                return False
            
            new_j = self._simulate_j_fast(historical_data, price)
            if not (new_j > prev_j and new_j < 20):
                return False
            
            angle = self._calculate_angle(prev_j, new_j)
            return angle > 70
            
        except Exception:
            return False
    
    def _check_price_conditions_detailed(self, historical_data: pd.DataFrame, target_date: str,
                                       price: float, prev_hdly: float, prev_j: float) -> Optional[Dict]:
        """详细检查价格条件（返回完整结果）"""
        try:
            new_hdly = self._simulate_hdly_fast(historical_data, price)
            hdly_condition = new_hdly > 0.1 and new_hdly > prev_hdly
            
            if not hdly_condition:
                return None
            
            new_j = self._simulate_j_fast(historical_data, price)
            j_increased = new_j > prev_j
            j_below_20 = new_j < 20
            angle = self._calculate_angle(prev_j, new_j)
            angle_condition = angle > 70
            
            if j_increased and j_below_20 and angle_condition:
                return {
                    'price': price,
                    'hdly_value': new_hdly,
                    'j_value': new_j,
                    'angle': angle
                }
            
            return None
            
        except Exception:
            return None
    
    def _simulate_hdly_fast(self, historical_data: pd.DataFrame, new_low: float) -> float:
        """快速模拟hdly计算"""
        try:
            # 使用最近的数据进行快速计算
            recent_lows = historical_data['low'].tail(20).tolist()
            if not recent_lows:
                return 0.0

            recent_lows.append(new_low)

            # 简化的hdly计算
            hdly_values = get_hdly_value(pd.Series(recent_lows))
            if hdly_values.empty:
                return 0.0

            return hdly_values.iloc[-1]
        except Exception:
            return 0.0
    
    def _simulate_j_fast(self, historical_data: pd.DataFrame, new_price: float) -> float:
        """快速模拟J值计算"""
        try:
            # 使用最近的数据进行快速计算
            recent_data = historical_data.tail(20).copy()
            if len(recent_data) == 0:
                return 0.0

            # 添加新数据点
            new_row = recent_data.iloc[-1].copy()
            new_row['high'] = new_price
            new_row['low'] = new_price
            new_row['close'] = new_price

            extended_data = pd.concat([recent_data, pd.DataFrame([new_row])], ignore_index=True)

            # 计算KDJ
            kdj_data = calculate_kdj_rsi(extended_data)
            if kdj_data.empty or 'J' not in kdj_data.columns:
                return 0.0

            return kdj_data['J'].iloc[-1]
        except Exception:
            return 0.0
    
    def predict_single_stock_optimized(self, stock_code: str, target_date: str) -> Dict:
        """优化的单股票预测"""
        try:
            # 1. 加载数据（使用缓存）
            data = self._load_stock_data(stock_code)
            if data is None:
                return {
                    'success': False,
                    'stock_code': stock_code,
                    'target_date': target_date,
                    'error': '无法加载股票数据'
                }
            
            # 2. 获取历史数据
            target_dt = pd.to_datetime(target_date)
            historical_data = data[data['date'] < target_dt].copy()
            
            if len(historical_data) < 30:
                return {
                    'success': False,
                    'stock_code': stock_code,
                    'target_date': target_date,
                    'error': '历史数据不足'
                }
            
            # 3. 计算历史指标
            hdly_values = get_hdly_value(historical_data['low'])
            kdj_rsi_data = calculate_kdj_rsi(historical_data)
            
            prev_hdly = hdly_values.iloc[-1]
            prev_j = kdj_rsi_data['J'].iloc[-1]
            
            # 4. 检查基础条件
            j_values = kdj_rsi_data['J'].tolist()
            if not self._check_j_is_lowest_in_period(j_values, len(j_values) - 1, 5):
                return {
                    'success': False,
                    'stock_code': stock_code,
                    'target_date': target_date,
                    'error': '前一个J值不是前5天的最低点'
                }
            
            # 5. 计算价格限制
            prev_close = historical_data['close'].iloc[-1]
            is_kechuang = self._is_kechuangban(stock_code)
            min_price, max_price = self._calculate_price_limits(prev_close, is_kechuang)
            
            # 6. 智能价格搜索
            valid_ranges = self._smart_price_search(
                historical_data, target_date, prev_close, prev_hdly, prev_j,
                min_price, max_price
            )
            
            # 7. 构建结果
            simplified_result = {
                'success': len(valid_ranges) > 0,
                'stock_code': stock_code,
                'target_date': target_date,
                'prev_close': prev_close,
                'valid_count': len(valid_ranges),
                'board_type': '科创板' if is_kechuang else '主板'
            }
            
            if valid_ranges:
                prices = [p['price'] for p in valid_ranges]
                simplified_result.update({
                    'price_range': {
                        'min': min(prices),
                        'max': max(prices),
                        'count': len(prices)
                    },
                    'potential_return': {
                        'min_pct': (min(prices) - prev_close) / prev_close * 100,
                        'max_pct': (max(prices) - prev_close) / prev_close * 100
                    }
                })
            
            return simplified_result
            
        except Exception as e:
            return {
                'success': False,
                'stock_code': stock_code,
                'target_date': target_date,
                'error': str(e)
            }
    
    def batch_predict_optimized(self, target_date: str, max_workers: int = 8,
                               chunk_size: int = 50) -> Dict:
        """优化的批量预测"""
        print(f"🚀 开始优化批量预测 {len(self.stock_codes)} 只股票...")
        print(f"📅 目标日期: {target_date}")
        print(f"🔧 并行线程数: {max_workers}")
        print(f"📦 分块大小: {chunk_size}")
        
        results = {}
        successful_predictions = 0
        completed_count = 0
        start_time = time.time()
        
        # 分块处理
        stock_chunks = [self.stock_codes[i:i + chunk_size] 
                       for i in range(0, len(self.stock_codes), chunk_size)]
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交分块任务
            chunk_futures = []
            for chunk in stock_chunks:
                future = executor.submit(self._process_stock_chunk, chunk, target_date)
                chunk_futures.append(future)
            
            # 处理完成的分块
            for future in as_completed(chunk_futures):
                try:
                    chunk_results = future.result()
                    results.update(chunk_results)
                    
                    # 统计成功数量
                    chunk_success = sum(1 for r in chunk_results.values() if r['success'])
                    successful_predictions += chunk_success
                    completed_count += len(chunk_results)
                    
                    # 进度报告
                    progress = completed_count / len(self.stock_codes) * 100
                    print(f"📊 进度: {completed_count}/{len(self.stock_codes)} ({progress:.1f}%)")
                    
                except Exception as e:
                    print(f"❌ 分块处理失败: {e}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return {
            'target_date': target_date,
            'total_stocks': len(self.stock_codes),
            'successful_predictions': successful_predictions,
            'success_rate': successful_predictions / len(self.stock_codes) * 100,
            'duration_seconds': total_time,  # 兼容原版本格式
            'total_time': total_time,
            'avg_time_per_stock': total_time / len(self.stock_codes),
            'parallel_workers': max_workers,
            'results': results
        }
    
    def _process_stock_chunk(self, stock_codes: List[str], target_date: str) -> Dict:
        """处理股票分块"""
        chunk_results = {}
        
        for stock_code in stock_codes:
            result = self.predict_single_stock_optimized(stock_code, target_date)
            chunk_results[stock_code] = result
        
        return chunk_results


def optimized_batch_predict_all_stocks(target_date: str,
                                     max_workers: int = 8,
                                     chunk_size: int = 50,
                                     cache_size: int = 100,
                                     save_csv: bool = True,
                                     show_top: int = 10) -> Dict:
    """
    优化的批量预测接口
    
    参数:
    target_date: 目标日期
    max_workers: 并行线程数
    chunk_size: 分块大小
    cache_size: 缓存大小
    save_csv: 是否保存CSV
    show_top: 显示前N个机会
    
    返回:
    Dict: 批量预测结果
    """
    predictor = OptimizedBatchPredictor(cache_size=cache_size)
    
    print(f"🚀 优化批量预测所有股票 (共 {len(predictor.stock_codes)} 只)")
    print(f"📅 目标日期: {target_date}")
    print(f"💾 缓存大小: {cache_size}")
    
    # 执行预测
    batch_result = predictor.batch_predict_optimized(target_date, max_workers, chunk_size)
    
    # 打印摘要
    from batch_predict_all import print_batch_summary
    print_batch_summary(batch_result)
    
    # 显示最佳机会
    if batch_result['successful_predictions'] > 0 and show_top > 0:
        print(f"\n✨ 前 {show_top} 个最佳投资机会:")
        
        # 获取机会列表
        opportunities = []
        for stock_code, result in batch_result['results'].items():
            if result['success'] and 'price_range' in result:
                opportunities.append({
                    'stock_code': stock_code,
                    'board_type': result['board_type'],
                    'prev_close': result['prev_close'],
                    'price_range': result['price_range'],
                    'potential_return': result['potential_return'],
                    'valid_count': result['valid_count']
                })
        
        # 按收益率排序
        opportunities.sort(key=lambda x: x['potential_return']['max_pct'], reverse=True)
        
        for i, opp in enumerate(opportunities[:show_top], 1):
            print(f"   {i:2d}. {opp['stock_code']} ({opp['board_type']}): "
                  f"价格 {opp['price_range']['min']:.2f}-{opp['price_range']['max']:.2f}, "
                  f"收益 {opp['potential_return']['min_pct']:.1f}%-{opp['potential_return']['max_pct']:.1f}%")
    
    # 保存CSV
    if save_csv and batch_result['successful_predictions'] > 0:
        from batch_predict_all import BatchPredictor
        temp_predictor = BatchPredictor()
        csv_file = temp_predictor.save_results_to_csv(batch_result)
        print(f"\n💾 详细结果已保存到: {csv_file}")
    
    return batch_result


if __name__ == "__main__":
    # 测试优化版本
    target_date = "2025-03-10"
    
    print("🧪 测试优化版本性能...")
    result = optimized_batch_predict_all_stocks(
        target_date=target_date,
        max_workers=8,
        chunk_size=30,
        cache_size=100,
        save_csv=False,
        show_top=5
    )
    
    print(f"\n📊 性能对比:")
    print(f"   总耗时: {result['total_time']:.2f} 秒")
    print(f"   平均耗时: {result['avg_time_per_stock']:.3f} 秒/股")
    print(f"   成功率: {result['success_rate']:.1f}%")
