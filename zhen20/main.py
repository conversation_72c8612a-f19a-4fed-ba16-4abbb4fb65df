"""
同花顺指标主程序
提供简洁的接口来计算和显示指标
"""

import pandas as pd
import numpy as np
from indicators import load_stock_data, calculate_indicators, get_indicator_values


def display_indicators(stock_code: str, date: str, n1: int = 9, n2: int = 34, n3: int = 1):
    """
    显示指定日期的所有指标值
    
    Args:
        stock_code: 股票代码
        date: 日期字符串
        n1: 短期周期参数
        n2: 长期周期参数  
        n3: A、B线系数
    """
    try:
        result = get_indicator_values(stock_code, date, n1, n2, n3)
        
        print(f"\n=== 股票 {stock_code} 在 {date} 的指标值 ===")
        print(f"短期 (N1={n1}): {result['短期']:.2f}")
        print(f"中期 (固定10): {result['中期']:.2f}")
        print(f"中长期 (固定20): {result['中长期']:.2f}")
        print(f"长期 (N2={n2}): {result['长期']:.2f}")
        
        print(f"\n=== 买入信号 ===")
        print(f"四线归零买: {result['四线归零买']}")
        print(f"白线下20买: {result['白线下20买']}")
        print(f"白穿红线买: {result['白穿红线买']}")
        print(f"白穿黄线买: {result['白穿黄线买']}")
        
        print(f"\n=== A、B线 ===")
        print(f"A线 (80*N3): {result['A']}")
        print(f"B线 (20*N3): {result['B']}")
        
        return result
        
    except Exception as e:
        print(f"计算指标时出错: {e}")
        return None


def batch_calculate(stock_code: str, start_date: str, end_date: str, n1: int = 9, n2: int = 34, n3: int = 1):
    """
    批量计算指定时间段的指标
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        n1, n2, n3: 参数
        
    Returns:
        包含所有指标的DataFrame
    """
    try:
        df = load_stock_data(stock_code)
        indicators = calculate_indicators(df, n1, n2, n3)
        
        # 筛选日期范围
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        mask = (df['date'] >= start_dt) & (df['date'] <= end_dt)
        
        result_df = df[mask][['date', 'open', 'close', 'high', 'low', 'volume']].copy()
        
        # 添加指标列
        for name, series in indicators.items():
            result_df[name] = series[mask].values
            
        return result_df
        
    except Exception as e:
        print(f"批量计算时出错: {e}")
        return None


def find_buy_signals(stock_code: str, start_date: str, end_date: str, n1: int = 9, n2: int = 34, n3: int = 1):
    """
    查找买入信号
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        n1, n2, n3: 参数
        
    Returns:
        包含买入信号的DataFrame
    """
    df = batch_calculate(stock_code, start_date, end_date, n1, n2, n3)
    if df is None:
        return None
    
    # 筛选有买入信号的日期
    buy_signals = df[
        (df['四线归零买'] != 0) | 
        (df['白线下20买'] != 0) | 
        (df['白穿红线买'] != 0) | 
        (df['白穿黄线买'] != 0)
    ].copy()
    
    if len(buy_signals) > 0:
        print(f"\n=== 股票 {stock_code} 在 {start_date} 到 {end_date} 期间的买入信号 ===")
        for _, row in buy_signals.iterrows():
            print(f"\n日期: {row['date'].strftime('%Y-%m-%d')}")
            print(f"收盘价: {row['close']:.2f}")
            if row['四线归零买'] != 0:
                print("  ✓ 四线归零买信号")
            if row['白线下20买'] != 0:
                print("  ✓ 白线下20买信号")
            if row['白穿红线买'] != 0:
                print("  ✓ 白穿红线买信号")
            if row['白穿黄线买'] != 0:
                print("  ✓ 白穿黄线买信号")
    else:
        print(f"在指定时间段内未发现买入信号")
    
    return buy_signals


if __name__ == "__main__":
    # 验证计算结果
    stock_code = "002203"
    
    print("=== 验证指标计算 ===")
    
    # 使用默认参数验证
    display_indicators(stock_code, "2024-07-16")
    display_indicators(stock_code, "2024-07-15")
    
    # 查找最近的买入信号
    print("\n" + "="*50)
    find_buy_signals(stock_code, "2024-07-01", "2024-07-31")
    
    # 显示最近几天的指标趋势
    print("\n" + "="*50)
    print("=== 最近一周指标趋势 ===")
    recent_data = batch_calculate(stock_code, "2024-07-10", "2024-07-20")
    if recent_data is not None:
        print(recent_data[['date', 'close', '短期', '长期']].to_string(index=False))
