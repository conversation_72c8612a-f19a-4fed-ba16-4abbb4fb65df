## 任务
将下面的同花顺指标代码转化为 python 代码

## 同花顺指标代码
```shell
N1:=3;
N2:=21;
N3:=1;
短期:100*(C-LLV(L,N1))/(HHV(C,N1)-LLV(L,N1)),colorwhite; 
中期:=100*(C-LLV(L,10))/(HHV(C,10)-LLV(L,10)),coloryellow; 
中长期:=100*(C-LLV(L,20))/(HHV(C,20)-LLV(L,20)),colormagenta; 
长期:100*(C-LLV(L,N2))/(HHV(C,N2)-LLV(L,N2)),COLORRED,LINETHICK2; 
四线归零买:=IF((短期<=6 AND 中期<=6 AND 中长期<=6 AND 长期<=6),-30,0),STICK,COLOR0000FF,LINETHICK3; 
白线下20买:=IF(短期<=20 AND 长期>=60,-30,0),STICK,COLOR00FFFF,LINETHICK3; 
白穿红线买:=IF(((CROSS(短期,长期)AND 长期<20)),-30,0),STICK,COLOR00FF00,LINETHICK3; 
白穿黄线买:=IF(((CROSS(短期,中期)AND 中期<30)),-30,0),STICK,COLORFF9150,LINETHICK3; 
A:80*N3,coloryellow; 
B:20*N3,coloryellow;
```

## 验证
股票代码： 002203
    日期： 2024-07-16
        短期：6.67
        长期：85.11
        A：80
        B：20

    日期： 2024-07-15
        短期：55.56
        长期：91.49
        A：80
        B：20

## 规范
1. 所有代码在 zhen20 文件夹内实现
2. 数据可以从根目录的 data 文件夹内读取，数据文件为 [股票代码].csv
