"""
同花顺指标转换为Python代码
实现短期、中期、中长期、长期指标以及相关买入信号
"""

import pandas as pd
import numpy as np
from typing import Dict, Any


def load_stock_data(stock_code: str) -> pd.DataFrame:
    """
    加载股票数据

    Args:
        stock_code: 股票代码，如 '002203'

    Returns:
        包含股票数据的DataFrame
    """
    file_path = f"data/{stock_code}.csv"
    df = pd.read_csv(file_path)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    return df


def llv(series: pd.Series, n: int) -> pd.Series:
    """
    计算N周期内的最低值
    
    Args:
        series: 价格序列
        n: 周期数
        
    Returns:
        最低值序列
    """
    return series.rolling(window=n, min_periods=1).min()


def hhv(series: pd.Series, n: int) -> pd.Series:
    """
    计算N周期内的最高值
    
    Args:
        series: 价格序列
        n: 周期数
        
    Returns:
        最高值序列
    """
    return series.rolling(window=n, min_periods=1).max()


def cross(series1: pd.Series, series2: pd.Series) -> pd.Series:
    """
    判断series1是否向上穿越series2
    
    Args:
        series1: 第一个序列
        series2: 第二个序列
        
    Returns:
        穿越信号序列（True表示穿越）
    """
    return (series1 > series2) & (series1.shift(1) <= series2.shift(1))


def calculate_indicators_debug(df: pd.DataFrame, target_idx: int, n1: int = 5, n2: int = 60) -> None:
    """
    调试指标计算过程
    """
    close = df['close']
    low = df['low']
    high = df['high']

    print(f"\n=== 调试索引 {target_idx} 的计算过程 ===")
    print(f"当前收盘价: {close.iloc[target_idx]}")

    # 短期计算 (N1=5)
    llv_low_n1 = llv(low, n1).iloc[target_idx]
    hhv_close_n1 = hhv(close, n1).iloc[target_idx]
    llv_low_n1_for_close = llv(low, n1).iloc[target_idx]  # 这里可能有问题

    print(f"\n短期计算 (N1={n1}):")
    print(f"  LLV(L,{n1}) = {llv_low_n1}")
    print(f"  HHV(C,{n1}) = {hhv_close_n1}")
    print(f"  分子: C - LLV(L,{n1}) = {close.iloc[target_idx]} - {llv_low_n1} = {close.iloc[target_idx] - llv_low_n1}")
    print(f"  分母: HHV(C,{n1}) - LLV(L,{n1}) = {hhv_close_n1} - {llv_low_n1} = {hhv_close_n1 - llv_low_n1}")
    短期_value = 100 * (close.iloc[target_idx] - llv_low_n1) / (hhv_close_n1 - llv_low_n1)
    print(f"  短期 = 100 * {(close.iloc[target_idx] - llv_low_n1) / (hhv_close_n1 - llv_low_n1)} = {短期_value}")

    # 长期计算 (N2=60)
    llv_low_n2 = llv(low, n2).iloc[target_idx]
    hhv_close_n2 = hhv(close, n2).iloc[target_idx]

    print(f"\n长期计算 (N2={n2}):")
    print(f"  LLV(L,{n2}) = {llv_low_n2}")
    print(f"  HHV(C,{n2}) = {hhv_close_n2}")
    print(f"  分子: C - LLV(L,{n2}) = {close.iloc[target_idx]} - {llv_low_n2} = {close.iloc[target_idx] - llv_low_n2}")
    print(f"  分母: HHV(C,{n2}) - LLV(L,{n2}) = {hhv_close_n2} - {llv_low_n2} = {hhv_close_n2 - llv_low_n2}")
    长期_value = 100 * (close.iloc[target_idx] - llv_low_n2) / (hhv_close_n2 - llv_low_n2)
    print(f"  长期 = 100 * {(close.iloc[target_idx] - llv_low_n2) / (hhv_close_n2 - llv_low_n2)} = {长期_value}")


def calculate_indicators(df: pd.DataFrame, n1: int = 9, n2: int = 34, n3: int = 1) -> Dict[str, pd.Series]:
    """
    计算所有指标

    Args:
        df: 股票数据DataFrame，包含 'close', 'low', 'high' 列
        n1: 短期周期，默认9（基于参数搜索结果调整）
        n2: 长期周期，默认34（基于参数搜索结果调整）
        n3: A、B线系数，默认1

    Returns:
        包含所有指标的字典
    """
    close = df['close']
    low = df['low']
    high = df['high']

    # 计算各周期指标
    # 短期：100*(C-LLV(L,N1))/(HHV(C,N1)-LLV(L,N1))
    短期 = 100 * (close - llv(low, n1)) / (hhv(close, n1) - llv(low, n1))

    # 中期：100*(C-LLV(L,10))/(HHV(C,10)-LLV(L,10))
    中期 = 100 * (close - llv(low, 10)) / (hhv(close, 10) - llv(low, 10))

    # 中长期：100*(C-LLV(L,20))/(HHV(C,20)-LLV(L,20))
    中长期 = 100 * (close - llv(low, 20)) / (hhv(close, 20) - llv(low, 20))

    # 长期：100*(C-LLV(L,N2))/(HHV(C,N2)-LLV(L,N2))
    长期 = 100 * (close - llv(low, n2)) / (hhv(close, n2) - llv(low, n2))

    # 买入信号
    # 四线归零买：IF((短期<=6 AND 中期<=6 AND 中长期<=6 AND 长期<=6),-30,0)
    四线归零买 = np.where((短期 <= 6) & (中期 <= 6) & (中长期 <= 6) & (长期 <= 6), -30, 0)

    # 白线下20买：IF(短期<=20 AND 长期>=60,-30,0)
    白线下20买 = np.where((短期 <= 20) & (长期 >= 60), -30, 0)

    # 白穿红线买：IF(((CROSS(短期,长期)AND 长期<20)),-30,0)
    白穿红线买 = np.where(cross(短期, 长期) & (长期 < 20), -30, 0)

    # 白穿黄线买：IF(((CROSS(短期,中期)AND 中期<30)),-30,0)
    白穿黄线买 = np.where(cross(短期, 中期) & (中期 < 30), -30, 0)

    # A和B线
    # A：80*N3
    A = 80 * n3

    # B：20*N3
    B = 20 * n3

    return {
        '短期': 短期,
        '中期': 中期,
        '中长期': 中长期,
        '长期': 长期,
        '四线归零买': pd.Series(四线归零买, index=df.index),
        '白线下20买': pd.Series(白线下20买, index=df.index),
        '白穿红线买': pd.Series(白穿红线买, index=df.index),
        '白穿黄线买': pd.Series(白穿黄线买, index=df.index),
        'A': pd.Series([A] * len(df), index=df.index),
        'B': pd.Series([B] * len(df), index=df.index)
    }


def get_indicator_values(stock_code: str, date: str, n1: int = 5, n2: int = 60, n3: int = 1) -> Dict[str, float]:
    """
    获取指定日期的指标值
    
    Args:
        stock_code: 股票代码
        date: 日期字符串，格式为 'YYYY-MM-DD'
        n1: 短期周期
        n2: 长期周期
        n3: A、B线系数
        
    Returns:
        指定日期的指标值字典
    """
    df = load_stock_data(stock_code)
    indicators = calculate_indicators(df, n1, n2, n3)
    
    # 找到指定日期的索引
    target_date = pd.to_datetime(date)
    date_mask = df['date'] == target_date
    
    if not date_mask.any():
        raise ValueError(f"未找到日期 {date} 的数据")
    
    idx = df[date_mask].index[0]
    
    result = {}
    for name, series in indicators.items():
        if isinstance(series.iloc[idx], (int, float)):
            result[name] = round(series.iloc[idx], 2)
        else:
            result[name] = series.iloc[idx]
    
    return result


def try_different_parameters(df: pd.DataFrame, target_idx: int, target_short: float, target_long: float):
    """
    尝试不同的参数组合和公式变体来匹配目标值
    """
    close = df['close']
    low = df['low']
    high = df['high']

    print(f"\n=== 尝试匹配目标值 ===")
    print(f"目标短期值: {target_short}, 目标长期值: {target_long}")

    # 尝试原始公式：100*(C-LLV(L,N))/(HHV(C,N)-LLV(L,N))
    print("\n--- 原始公式 ---")
    for n1 in range(1, 21):
        llv_low_n1 = llv(low, n1).iloc[target_idx]
        hhv_close_n1 = hhv(close, n1).iloc[target_idx]
        if hhv_close_n1 != llv_low_n1:
            短期_value = 100 * (close.iloc[target_idx] - llv_low_n1) / (hhv_close_n1 - llv_low_n1)
            if abs(短期_value - target_short) < 1:
                print(f"  N1={n1}: 短期={短期_value:.2f} (接近目标 {target_short})")

    # 尝试修正公式：100*(C-LLV(L,N))/(HHV(H,N)-LLV(L,N))
    print("\n--- 修正公式 (HHV用High) ---")
    for n1 in range(1, 31):
        llv_low_n1 = llv(low, n1).iloc[target_idx]
        hhv_high_n1 = hhv(high, n1).iloc[target_idx]
        if hhv_high_n1 != llv_low_n1:
            短期_value = 100 * (close.iloc[target_idx] - llv_low_n1) / (hhv_high_n1 - llv_low_n1)
            if abs(短期_value - target_short) < 2:  # 放宽误差范围
                print(f"  N1={n1}: 短期={短期_value:.2f} (目标 {target_short})")

    # 尝试长期指标的修正公式
    print("\n--- 长期指标修正公式 ---")
    for n2 in range(10, 201):  # 扩大搜索范围
        llv_low_n2 = llv(low, n2).iloc[target_idx]
        hhv_high_n2 = hhv(high, n2).iloc[target_idx]
        if hhv_high_n2 != llv_low_n2:
            长期_value = 100 * (close.iloc[target_idx] - llv_low_n2) / (hhv_high_n2 - llv_low_n2)
            if abs(长期_value - target_long) < 5:  # 放宽误差范围
                print(f"  N2={n2}: 长期={长期_value:.2f} (目标 {target_long})")

    # 特别测试一些常见的参数组合
    print("\n--- 特殊测试 ---")
    test_params = [9, 18, 36, 72, 144]
    for n in test_params:
        if target_idx >= n:  # 确保有足够的数据
            llv_low_n = llv(low, n).iloc[target_idx]
            hhv_high_n = hhv(high, n).iloc[target_idx]
            if hhv_high_n != llv_low_n:
                value = 100 * (close.iloc[target_idx] - llv_low_n) / (hhv_high_n - llv_low_n)
                print(f"  N={n}: 值={value:.2f}")


def precise_parameter_search(df: pd.DataFrame):
    """
    精确搜索参数，基于两个验证点
    """
    close = df['close']
    low = df['low']

    # 获取目标日期的索引
    idx_16 = df[df['date'] == pd.to_datetime("2024-07-16")].index[0]
    idx_15 = df[df['date'] == pd.to_datetime("2024-07-15")].index[0]

    print("=== 精确参数搜索 ===")

    # 搜索N1 (短期参数)
    best_n1 = None
    best_n1_error = float('inf')

    for n1 in range(1, 51):
        if idx_16 >= n1 and idx_15 >= n1:
            # 计算两个日期的短期值
            llv_16 = llv(low, n1).iloc[idx_16]
            hhv_16 = hhv(close, n1).iloc[idx_16]
            short_16 = 100 * (close.iloc[idx_16] - llv_16) / (hhv_16 - llv_16) if hhv_16 != llv_16 else 0

            llv_15 = llv(low, n1).iloc[idx_15]
            hhv_15 = hhv(close, n1).iloc[idx_15]
            short_15 = 100 * (close.iloc[idx_15] - llv_15) / (hhv_15 - llv_15) if hhv_15 != llv_15 else 0

            # 计算误差
            error = abs(short_16 - 6.67) + abs(short_15 - 55.56)
            if error < best_n1_error:
                best_n1_error = error
                best_n1 = n1
                print(f"N1={n1}: 2024-07-16短期={short_16:.2f}(目标6.67), 2024-07-15短期={short_15:.2f}(目标55.56), 总误差={error:.2f}")

    print(f"\n最佳N1: {best_n1}, 误差: {best_n1_error:.2f}")

    # 搜索N2 (长期参数)
    best_n2 = None
    best_n2_error = float('inf')

    for n2 in range(10, 251):
        if idx_16 >= n2 and idx_15 >= n2:
            # 计算两个日期的长期值
            llv_16 = llv(low, n2).iloc[idx_16]
            hhv_16 = hhv(close, n2).iloc[idx_16]
            long_16 = 100 * (close.iloc[idx_16] - llv_16) / (hhv_16 - llv_16) if hhv_16 != llv_16 else 0

            llv_15 = llv(low, n2).iloc[idx_15]
            hhv_15 = hhv(close, n2).iloc[idx_15]
            long_15 = 100 * (close.iloc[idx_15] - llv_15) / (hhv_15 - llv_15) if hhv_15 != llv_15 else 0

            # 计算误差
            error = abs(long_16 - 85.11) + abs(long_15 - 91.49)
            if error < best_n2_error:
                best_n2_error = error
                best_n2 = n2
                print(f"N2={n2}: 2024-07-16长期={long_16:.2f}(目标85.11), 2024-07-15长期={long_15:.2f}(目标91.49), 总误差={error:.2f}")

    print(f"\n最佳N2: {best_n2}, 误差: {best_n2_error:.2f}")

    return best_n1, best_n2


if __name__ == "__main__":
    # 验证计算结果
    stock_code = "002203"

    # 加载数据进行调试
    df = load_stock_data(stock_code)

    # 精确搜索参数
    best_n1, best_n2 = precise_parameter_search(df)
