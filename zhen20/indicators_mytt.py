"""
使用 MyTT 库实现同花顺指标的精确计算
MyTT 库专门用于将通达信、同花顺等指标公式转换为Python代码
"""

import pandas as pd
import numpy as np
from MyTT import *
from typing import Dict, Tuple
import os


def load_stock_data(stock_code: str) -> pd.DataFrame:
    """
    加载股票数据

    Args:
        stock_code: 股票代码

    Returns:
        包含股票数据的DataFrame
    """
    data_path = f"data/{stock_code}.csv"
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")

    df = pd.read_csv(data_path)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)

    return df


def calculate_indicators_mytt(df: pd.DataFrame, n1: int = 9, n2: int = 34, n3: int = 1) -> Dict[str, np.ndarray]:
    """
    使用 MyTT 库计算同花顺指标
    
    原始同花顺指标公式：
    短期:100*(C-LLV(L,N1))/(HHV(C,N1)-LLV(L,N1))
    中期:=100*(C-LLV(L,10))/(HHV(C,10)-LLV(L,10))
    中长期:=100*(C-LLV(L,20))/(HHV(C,20)-LLV(L,20))
    长期:100*(C-LLV(L,N2))/(HHV(C,N2)-LLV(L,N2))
    
    Args:
        df: 股票数据DataFrame
        n1: 短期周期参数
        n2: 长期周期参数
        n3: A、B线系数
        
    Returns:
        包含所有指标的字典
    """
    # 提取基础数据
    CLOSE = df['close'].values
    HIGH = df['high'].values
    LOW = df['low'].values
    OPEN = df['open'].values
    
    # 计算各周期指标 - 使用 MyTT 库的函数
    # 短期：100*(C-LLV(L,N1))/(HHV(C,N1)-LLV(L,N1))
    短期 = 100 * (CLOSE - LLV(LOW, n1)) / (HHV(CLOSE, n1) - LLV(LOW, n1))
    
    # 中期：100*(C-LLV(L,10))/(HHV(C,10)-LLV(L,10))
    中期 = 100 * (CLOSE - LLV(LOW, 10)) / (HHV(CLOSE, 10) - LLV(LOW, 10))
    
    # 中长期：100*(C-LLV(L,20))/(HHV(C,20)-LLV(L,20))
    中长期 = 100 * (CLOSE - LLV(LOW, 20)) / (HHV(CLOSE, 20) - LLV(LOW, 20))
    
    # 长期：100*(C-LLV(L,N2))/(HHV(C,N2)-LLV(L,N2))
    长期 = 100 * (CLOSE - LLV(LOW, n2)) / (HHV(CLOSE, n2) - LLV(LOW, n2))
    
    # 买入信号计算
    # 四线归零买：IF((短期<=6 AND 中期<=6 AND 中长期<=6 AND 长期<=6),-30,0)
    四线归零买 = IF((短期 <= 6) & (中期 <= 6) & (中长期 <= 6) & (长期 <= 6), -30, 0)
    
    # 白线下20买：IF(短期<=20 AND 长期>=60,-30,0)
    白线下20买 = IF((短期 <= 20) & (长期 >= 60), -30, 0)
    
    # 白穿红线买：IF(((CROSS(短期,长期)AND 长期<20)),-30,0)
    白穿红线买 = IF(CROSS(短期, 长期) & (长期 < 20), -30, 0)
    
    # 白穿黄线买：IF(((CROSS(短期,中期)AND 中期<30)),-30,0)
    白穿黄线买 = IF(CROSS(短期, 中期) & (中期 < 30), -30, 0)
    
    # A和B线
    # A：80*N3
    A = np.full_like(CLOSE, 80 * n3)
    
    # B：20*N3
    B = np.full_like(CLOSE, 20 * n3)
    
    return {
        '短期': 短期,
        '中期': 中期,
        '中长期': 中长期,
        '长期': 长期,
        '四线归零买': 四线归零买,
        '白线下20买': 白线下20买,
        '白穿红线买': 白穿红线买,
        '白穿黄线买': 白穿黄线买,
        'A': A,
        'B': B
    }


def get_indicator_values_mytt(stock_code: str, date: str, n1: int = 9, n2: int = 34, n3: int = 1) -> Dict[str, float]:
    """
    获取指定日期的指标值
    
    Args:
        stock_code: 股票代码
        date: 日期字符串
        n1, n2, n3: 参数
        
    Returns:
        包含指标值的字典
    """
    df = load_stock_data(stock_code)
    indicators = calculate_indicators_mytt(df, n1, n2, n3)
    
    # 找到指定日期的索引
    target_date = pd.to_datetime(date)
    date_mask = df['date'] == target_date
    
    if not date_mask.any():
        raise ValueError(f"未找到日期 {date} 的数据")
    
    idx = df[date_mask].index[0]
    
    result = {}
    for name, values in indicators.items():
        if isinstance(values, np.ndarray) and len(values) > idx:
            result[name] = float(values[idx])
        else:
            result[name] = 0.0
    
    return result


def test_formula_variants(df: pd.DataFrame, target_dates: list, expected_values: dict):
    """
    测试不同的公式变体以找到正确的实现

    Args:
        df: 股票数据
        target_dates: 目标日期列表
        expected_values: 期望值字典
    """
    print("=== 测试不同公式变体 ===")

    # 获取目标日期的索引
    indices = {}
    for date in target_dates:
        target_dt = pd.to_datetime(date)
        mask = df['date'] == target_dt
        if mask.any():
            indices[date] = df[mask].index[0]

    CLOSE = df['close'].values
    HIGH = df['high'].values
    LOW = df['low'].values

    # 测试不同的公式变体
    variants = [
        ("原始公式", lambda n: 100 * (CLOSE - LLV(LOW, n)) / (HHV(CLOSE, n) - LLV(LOW, n))),
        ("变体1: HHV用HIGH", lambda n: 100 * (CLOSE - LLV(LOW, n)) / (HHV(HIGH, n) - LLV(LOW, n))),
        ("变体2: 分母用HIGH-LOW", lambda n: 100 * (CLOSE - LLV(LOW, n)) / (HHV(HIGH, n) - LLV(LOW, n))),
        ("变体3: 分子用HIGH", lambda n: 100 * (HIGH - LLV(LOW, n)) / (HHV(HIGH, n) - LLV(LOW, n))),
        ("变体4: KDJ类似公式", lambda n: 100 * (CLOSE - LLV(LOW, n)) / (HHV(HIGH, n) - LLV(LOW, n))),
    ]

    # 测试一些常见的参数值
    test_params = [
        (9, 34), (5, 60), (13, 21), (8, 55), (6, 89), (3, 144)
    ]

    best_matches = []

    for variant_name, formula_func in variants:
        print(f"\n--- {variant_name} ---")

        for n1, n2 in test_params:
            try:
                # 计算短期和长期指标
                short_values = formula_func(n1)
                long_values = formula_func(n2)

                total_error = 0
                results = {}

                for date in target_dates:
                    if date in indices:
                        idx = indices[date]
                        expected = expected_values.get(date, {})

                        short_val = short_values[idx] if not np.isnan(short_values[idx]) else 0
                        long_val = long_values[idx] if not np.isnan(long_values[idx]) else 0

                        results[date] = {'短期': short_val, '长期': long_val}

                        if '短期' in expected:
                            total_error += abs(short_val - expected['短期'])
                        if '长期' in expected:
                            total_error += abs(long_val - expected['长期'])

                if total_error < 50:  # 放宽误差范围
                    best_matches.append((variant_name, n1, n2, total_error, results))
                    print(f"  参数 N1={n1}, N2={n2}, 总误差={total_error:.2f}")
                    for date, vals in results.items():
                        expected = expected_values.get(date, {})
                        print(f"    {date}: 短期={vals['短期']:.2f}(期望{expected.get('短期', 'N/A')}), 长期={vals['长期']:.2f}(期望{expected.get('长期', 'N/A')})")

            except Exception as e:
                continue

    # 显示最佳匹配
    if best_matches:
        best_matches.sort(key=lambda x: x[3])
        print(f"\n=== 最佳匹配结果 ===")
        for i, (variant, n1, n2, error, results) in enumerate(best_matches[:5]):
            print(f"{i+1}. {variant}, N1={n1}, N2={n2}, 误差={error:.2f}")
    else:
        print("\n未找到较好的匹配结果")


def test_reverse_engineering(df: pd.DataFrame, target_dates: list, expected_values: dict):
    """
    反向工程：根据期望值推算可能的参数和公式
    """
    print("\n=== 反向工程分析 ===")

    indices = {}
    for date in target_dates:
        target_dt = pd.to_datetime(date)
        mask = df['date'] == target_dt
        if mask.any():
            indices[date] = df[mask].index[0]

    CLOSE = df['close'].values
    HIGH = df['high'].values
    LOW = df['low'].values

    for date in target_dates:
        if date not in indices:
            continue

        idx = indices[date]
        expected = expected_values.get(date, {})

        print(f"\n--- 分析 {date} ---")
        print(f"当日数据: 收盘={CLOSE[idx]:.4f}, 最高={HIGH[idx]:.4f}, 最低={LOW[idx]:.4f}")

        if '短期' in expected:
            target_short = expected['短期']
            print(f"目标短期值: {target_short}")

            # 尝试反推参数
            for n in range(1, 31):
                if idx >= n:
                    llv_val = LLV(LOW, n)[idx]
                    hhv_close = HHV(CLOSE, n)[idx]
                    hhv_high = HHV(HIGH, n)[idx]

                    # 原始公式
                    if hhv_close != llv_val:
                        calc1 = 100 * (CLOSE[idx] - llv_val) / (hhv_close - llv_val)
                        if abs(calc1 - target_short) < 1:
                            print(f"  原始公式 N={n}: {calc1:.2f} ≈ {target_short}")

                    # 变体公式
                    if hhv_high != llv_val:
                        calc2 = 100 * (CLOSE[idx] - llv_val) / (hhv_high - llv_val)
                        if abs(calc2 - target_short) < 1:
                            print(f"  变体公式 N={n}: {calc2:.2f} ≈ {target_short}")

        if '长期' in expected:
            target_long = expected['长期']
            print(f"目标长期值: {target_long}")

            # 尝试反推参数
            for n in range(20, 151):
                if idx >= n:
                    llv_val = LLV(LOW, n)[idx]
                    hhv_close = HHV(CLOSE, n)[idx]
                    hhv_high = HHV(HIGH, n)[idx]

                    # 原始公式
                    if hhv_close != llv_val:
                        calc1 = 100 * (CLOSE[idx] - llv_val) / (hhv_close - llv_val)
                        if abs(calc1 - target_long) < 1:
                            print(f"  原始公式 N={n}: {calc1:.2f} ≈ {target_long}")

                    # 变体公式
                    if hhv_high != llv_val:
                        calc2 = 100 * (CLOSE[idx] - llv_val) / (hhv_high - llv_val)
                        if abs(calc2 - target_long) < 1:
                            print(f"  变体公式 N={n}: {calc2:.2f} ≈ {target_long}")


if __name__ == "__main__":
    # 验证计算结果
    stock_code = "002203"
    
    # 加载数据
    df = load_stock_data(stock_code)
    
    # 验证数据
    target_dates = ["2024-07-15", "2024-07-16"]
    expected_values = {
        "2024-07-16": {"短期": 6.67, "长期": 85.11, "A": 80, "B": 20},
        "2024-07-15": {"短期": 55.56, "长期": 91.49, "A": 80, "B": 20}
    }
    
    # 测试公式变体
    test_formula_variants(df, target_dates, expected_values)

    # 反向工程分析
    test_reverse_engineering(df, target_dates, expected_values)
    
    # 使用默认参数测试
    print("\n=== 使用默认参数测试 ===")
    for date in target_dates:
        try:
            result = get_indicator_values_mytt(stock_code, date)
            expected = expected_values.get(date, {})
            
            print(f"\n{date} 的计算结果:")
            print(f"  短期: {result['短期']:.2f} (期望: {expected.get('短期', 'N/A')})")
            print(f"  长期: {result['长期']:.2f} (期望: {expected.get('长期', 'N/A')})")
            print(f"  A线: {result['A']:.2f} (期望: {expected.get('A', 'N/A')})")
            print(f"  B线: {result['B']:.2f} (期望: {expected.get('B', 'N/A')})")
            
        except Exception as e:
            print(f"计算 {date} 时出错: {e}")
