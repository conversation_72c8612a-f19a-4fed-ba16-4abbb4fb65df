"""
使用 MyTT 库实现同花顺指标的精确计算 - 改进版本
基于反向工程分析的结果，使用修正的公式
"""

import pandas as pd
import numpy as np
from MyTT import *
from typing import Dict, Tuple
import os


def load_stock_data(stock_code: str) -> pd.DataFrame:
    """加载股票数据"""
    data_path = f"data/{stock_code}.csv"
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    df = pd.read_csv(data_path)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    return df


def calculate_indicators_v2(df: pd.DataFrame, n1: int = 9, n2: int = 89, n3: int = 1) -> Dict[str, np.ndarray]:
    """
    使用改进的公式计算同花顺指标
    
    基于反向工程分析，使用以下修正：
    1. 使用 HHV(HIGH, N) 而不是 HHV(CLOSE, N)
    2. 调整默认参数：N1=9, N2=89（基于分析结果）
    
    修正后的公式：
    短期: 100*(C-LLV(L,N1))/(HHV(H,N1)-LLV(L,N1))
    长期: 100*(C-LLV(L,N2))/(HHV(H,N2)-LLV(L,N2))
    """
    # 提取基础数据
    CLOSE = df['close'].values
    HIGH = df['high'].values
    LOW = df['low'].values
    OPEN = df['open'].values
    
    # 使用修正的公式计算各周期指标
    # 短期：100*(C-LLV(L,N1))/(HHV(H,N1)-LLV(L,N1))
    短期 = 100 * (CLOSE - LLV(LOW, n1)) / (HHV(HIGH, n1) - LLV(LOW, n1))
    
    # 中期：100*(C-LLV(L,10))/(HHV(H,10)-LLV(L,10))
    中期 = 100 * (CLOSE - LLV(LOW, 10)) / (HHV(HIGH, 10) - LLV(LOW, 10))
    
    # 中长期：100*(C-LLV(L,20))/(HHV(H,20)-LLV(L,20))
    中长期 = 100 * (CLOSE - LLV(LOW, 20)) / (HHV(HIGH, 20) - LLV(LOW, 20))
    
    # 长期：100*(C-LLV(L,N2))/(HHV(H,N2)-LLV(L,N2))
    长期 = 100 * (CLOSE - LLV(LOW, n2)) / (HHV(HIGH, n2) - LLV(LOW, n2))
    
    # 买入信号计算
    四线归零买 = IF((短期 <= 6) & (中期 <= 6) & (中长期 <= 6) & (长期 <= 6), -30, 0)
    白线下20买 = IF((短期 <= 20) & (长期 >= 60), -30, 0)
    白穿红线买 = IF(CROSS(短期, 长期) & (长期 < 20), -30, 0)
    白穿黄线买 = IF(CROSS(短期, 中期) & (中期 < 30), -30, 0)
    
    # A和B线
    A = np.full_like(CLOSE, 80 * n3)
    B = np.full_like(CLOSE, 20 * n3)
    
    return {
        '短期': 短期,
        '中期': 中期,
        '中长期': 中长期,
        '长期': 长期,
        '四线归零买': 四线归零买,
        '白线下20买': 白线下20买,
        '白穿红线买': 白穿红线买,
        '白穿黄线买': 白穿黄线买,
        'A': A,
        'B': B
    }


def find_best_parameters(df: pd.DataFrame, target_dates: list, expected_values: dict):
    """
    寻找最佳参数组合
    """
    print("=== 寻找最佳参数组合 ===")
    
    # 获取目标日期的索引
    indices = {}
    for date in target_dates:
        target_dt = pd.to_datetime(date)
        mask = df['date'] == target_dt
        if mask.any():
            indices[date] = df[mask].index[0]
    
    CLOSE = df['close'].values
    HIGH = df['high'].values
    LOW = df['low'].values
    
    best_combinations = []
    
    # 基于反向工程的结果，重点测试这些参数范围
    n1_candidates = list(range(1, 21))  # 扩大短期参数范围
    n2_candidates = list(range(20, 201, 5))  # 扩大长期参数范围
    
    for n1 in n1_candidates:
        for n2 in n2_candidates:
            try:
                total_error = 0
                results = {}
                
                for date in target_dates:
                    if date not in indices:
                        continue
                        
                    idx = indices[date]
                    expected = expected_values.get(date, {})
                    
                    # 使用修正公式计算
                    short_val = 100 * (CLOSE[idx] - LLV(LOW, n1)[idx]) / (HHV(HIGH, n1)[idx] - LLV(LOW, n1)[idx])
                    long_val = 100 * (CLOSE[idx] - LLV(LOW, n2)[idx]) / (HHV(HIGH, n2)[idx] - LLV(LOW, n2)[idx])
                    
                    if np.isnan(short_val):
                        short_val = 0
                    if np.isnan(long_val):
                        long_val = 0
                    
                    results[date] = {'短期': short_val, '长期': long_val}
                    
                    # 计算误差
                    if '短期' in expected:
                        total_error += abs(short_val - expected['短期'])
                    if '长期' in expected:
                        total_error += abs(long_val - expected['长期'])
                
                if total_error < 100:  # 放宽误差范围
                    best_combinations.append((n1, n2, total_error, results))
                    
            except Exception as e:
                continue
    
    # 排序并显示结果
    best_combinations.sort(key=lambda x: x[2])
    
    print(f"找到 {len(best_combinations)} 个较好的参数组合:")
    for i, (n1, n2, error, results) in enumerate(best_combinations[:10]):
        print(f"\n{i+1}. N1={n1}, N2={n2}, 总误差={error:.2f}")
        for date, vals in results.items():
            expected = expected_values.get(date, {})
            print(f"  {date}: 短期={vals['短期']:.2f}(期望{expected.get('短期', 'N/A')}), 长期={vals['长期']:.2f}(期望{expected.get('长期', 'N/A')})")
    
    return best_combinations


def get_indicator_values_v2(stock_code: str, date: str, n1: int = 9, n2: int = 89, n3: int = 1) -> Dict[str, float]:
    """获取指定日期的指标值 - 改进版本"""
    df = load_stock_data(stock_code)
    indicators = calculate_indicators_v2(df, n1, n2, n3)
    
    # 找到指定日期的索引
    target_date = pd.to_datetime(date)
    date_mask = df['date'] == target_date
    
    if not date_mask.any():
        raise ValueError(f"未找到日期 {date} 的数据")
    
    idx = df[date_mask].index[0]
    
    result = {}
    for name, values in indicators.items():
        if isinstance(values, np.ndarray) and len(values) > idx:
            result[name] = float(values[idx])
        else:
            result[name] = 0.0
    
    return result


def create_final_implementation(best_params: tuple):
    """
    基于最佳参数创建最终实现
    """
    n1, n2 = best_params[:2]
    
    print(f"\n=== 创建最终实现 (N1={n1}, N2={n2}) ===")
    
    final_code = f'''
def calculate_tonghuashun_indicators(df: pd.DataFrame, n1: int = {n1}, n2: int = {n2}, n3: int = 1):
    """
    同花顺指标的最终实现
    
    基于参数优化结果：N1={n1}, N2={n2}
    使用修正公式：HHV(HIGH, N) 而不是 HHV(CLOSE, N)
    """
    from MyTT import *
    
    CLOSE = df['close'].values
    HIGH = df['high'].values
    LOW = df['low'].values
    
    # 修正后的公式
    短期 = 100 * (CLOSE - LLV(LOW, n1)) / (HHV(HIGH, n1) - LLV(LOW, n1))
    中期 = 100 * (CLOSE - LLV(LOW, 10)) / (HHV(HIGH, 10) - LLV(LOW, 10))
    中长期 = 100 * (CLOSE - LLV(LOW, 20)) / (HHV(HIGH, 20) - LLV(LOW, 20))
    长期 = 100 * (CLOSE - LLV(LOW, n2)) / (HHV(HIGH, n2) - LLV(LOW, n2))
    
    # 买入信号
    四线归零买 = IF((短期 <= 6) & (中期 <= 6) & (中长期 <= 6) & (长期 <= 6), -30, 0)
    白线下20买 = IF((短期 <= 20) & (长期 >= 60), -30, 0)
    白穿红线买 = IF(CROSS(短期, 长期) & (长期 < 20), -30, 0)
    白穿黄线买 = IF(CROSS(短期, 中期) & (中期 < 30), -30, 0)
    
    # A和B线
    A = np.full_like(CLOSE, 80 * n3)
    B = np.full_like(CLOSE, 20 * n3)
    
    return {{
        '短期': 短期, '中期': 中期, '中长期': 中长期, '长期': 长期,
        '四线归零买': 四线归零买, '白线下20买': 白线下20买,
        '白穿红线买': 白穿红线买, '白穿黄线买': 白穿黄线买,
        'A': A, 'B': B
    }}
'''
    
    print(final_code)
    return final_code


if __name__ == "__main__":
    # 验证计算结果
    stock_code = "002203"
    
    # 加载数据
    df = load_stock_data(stock_code)
    
    # 验证数据
    target_dates = ["2024-07-15", "2024-07-16"]
    expected_values = {
        "2024-07-16": {"短期": 6.67, "长期": 85.11, "A": 80, "B": 20},
        "2024-07-15": {"短期": 55.56, "长期": 91.49, "A": 80, "B": 20}
    }
    
    # 寻找最佳参数
    best_combinations = find_best_parameters(df, target_dates, expected_values)
    
    if best_combinations:
        # 使用最佳参数测试
        best_n1, best_n2 = best_combinations[0][:2]
        print(f"\n=== 使用最佳参数测试 (N1={best_n1}, N2={best_n2}) ===")
        
        for date in target_dates:
            try:
                result = get_indicator_values_v2(stock_code, date, best_n1, best_n2)
                expected = expected_values.get(date, {})
                
                print(f"\n{date} 的计算结果:")
                print(f"  短期: {result['短期']:.2f} (期望: {expected.get('短期', 'N/A')})")
                print(f"  长期: {result['长期']:.2f} (期望: {expected.get('长期', 'N/A')})")
                print(f"  A线: {result['A']:.2f} (期望: {expected.get('A', 'N/A')})")
                print(f"  B线: {result['B']:.2f} (期望: {expected.get('B', 'N/A')})")
                
            except Exception as e:
                print(f"计算 {date} 时出错: {e}")
        
        # 创建最终实现代码
        create_final_implementation(best_combinations[0])
    else:
        print("未找到合适的参数组合")
