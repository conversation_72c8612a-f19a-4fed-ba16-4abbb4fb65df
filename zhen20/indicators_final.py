"""
同花顺指标的最终准确实现
使用 MyTT 库和准确的参数值：N1=3, N2=21, N3=1
"""

import pandas as pd
import numpy as np
from MyTT import *
from typing import Dict
import os


def load_stock_data(stock_code: str) -> pd.DataFrame:
    """加载股票数据"""
    data_path = f"data/{stock_code}.csv"
    if not os.path.exists(data_path):
        raise FileNotFoundError(f"数据文件不存在: {data_path}")
    
    df = pd.read_csv(data_path)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)
    return df


def calculate_tonghuashun_indicators(df: pd.DataFrame) -> Dict[str, np.ndarray]:
    """
    计算同花顺指标 - 使用准确的参数值
    
    参数值：
    N1 = 3
    N2 = 21
    N3 = 1
    
    指标公式：
    短期: 100*(C-LLV(L,N1))/(HHV(C,N1)-LLV(L,N1))
    中期: 100*(C-LLV(L,10))/(HHV(C,10)-LLV(L,10))
    中长期: 100*(C-LLV(L,20))/(HHV(C,20)-LLV(L,20))
    长期: 100*(C-LLV(L,N2))/(HHV(C,N2)-LLV(L,N2))
    """
    # 参数定义
    N1 = 3
    N2 = 21
    N3 = 1
    
    # 提取基础数据
    CLOSE = df['close'].values
    HIGH = df['high'].values
    LOW = df['low'].values
    OPEN = df['open'].values
    
    # 计算各周期指标 - 尝试使用 HHV(HIGH, N) 而不是 HHV(CLOSE, N)
    # 短期：100*(C-LLV(L,N1))/(HHV(H,N1)-LLV(L,N1))
    短期 = 100 * (CLOSE - LLV(LOW, N1)) / (HHV(HIGH, N1) - LLV(LOW, N1))

    # 中期：100*(C-LLV(L,10))/(HHV(H,10)-LLV(L,10))
    中期 = 100 * (CLOSE - LLV(LOW, 10)) / (HHV(HIGH, 10) - LLV(LOW, 10))

    # 中长期：100*(C-LLV(L,20))/(HHV(H,20)-LLV(L,20))
    中长期 = 100 * (CLOSE - LLV(LOW, 20)) / (HHV(HIGH, 20) - LLV(LOW, 20))

    # 长期：100*(C-LLV(L,N2))/(HHV(H,N2)-LLV(L,N2))
    长期 = 100 * (CLOSE - LLV(LOW, N2)) / (HHV(HIGH, N2) - LLV(LOW, N2))
    
    # 买入信号计算
    # 四线归零买：IF((短期<=6 AND 中期<=6 AND 中长期<=6 AND 长期<=6),-30,0)
    四线归零买 = IF((短期 <= 6) & (中期 <= 6) & (中长期 <= 6) & (长期 <= 6), -30, 0)
    
    # 白线下20买：IF(短期<=20 AND 长期>=60,-30,0)
    白线下20买 = IF((短期 <= 20) & (长期 >= 60), -30, 0)
    
    # 白穿红线买：IF(((CROSS(短期,长期)AND 长期<20)),-30,0)
    白穿红线买 = IF(CROSS(短期, 长期) & (长期 < 20), -30, 0)
    
    # 白穿黄线买：IF(((CROSS(短期,中期)AND 中期<30)),-30,0)
    白穿黄线买 = IF(CROSS(短期, 中期) & (中期 < 30), -30, 0)
    
    # A和B线
    # A：80*N3
    A = np.full_like(CLOSE, 80 * N3)
    
    # B：20*N3
    B = np.full_like(CLOSE, 20 * N3)
    
    return {
        '短期': 短期,
        '中期': 中期,
        '中长期': 中长期,
        '长期': 长期,
        '四线归零买': 四线归零买,
        '白线下20买': 白线下20买,
        '白穿红线买': 白穿红线买,
        '白穿黄线买': 白穿黄线买,
        'A': A,
        'B': B
    }


def get_indicator_values(stock_code: str, date: str) -> Dict[str, float]:
    """
    获取指定日期的指标值
    
    Args:
        stock_code: 股票代码
        date: 日期字符串 (YYYY-MM-DD)
        
    Returns:
        包含指标值的字典
    """
    df = load_stock_data(stock_code)
    indicators = calculate_tonghuashun_indicators(df)
    
    # 找到指定日期的索引
    target_date = pd.to_datetime(date)
    date_mask = df['date'] == target_date
    
    if not date_mask.any():
        raise ValueError(f"未找到日期 {date} 的数据")
    
    idx = df[date_mask].index[0]
    
    result = {}
    for name, values in indicators.items():
        if isinstance(values, np.ndarray) and len(values) > idx:
            value = values[idx]
            if np.isnan(value):
                result[name] = 0.0
            else:
                result[name] = float(value)
        else:
            result[name] = 0.0
    
    return result


def display_indicators(stock_code: str, date: str):
    """显示指定日期的所有指标值"""
    try:
        result = get_indicator_values(stock_code, date)
        
        print(f"\n=== 股票 {stock_code} 在 {date} 的指标值 ===")
        print(f"短期 (N1=3): {result['短期']:.2f}")
        print(f"中期 (固定10): {result['中期']:.2f}")
        print(f"中长期 (固定20): {result['中长期']:.2f}")
        print(f"长期 (N2=21): {result['长期']:.2f}")
        
        print(f"\n=== 买入信号 ===")
        signals = []
        if result['四线归零买'] != 0:
            signals.append("四线归零买")
        if result['白线下20买'] != 0:
            signals.append("白线下20买")
        if result['白穿红线买'] != 0:
            signals.append("白穿红线买")
        if result['白穿黄线买'] != 0:
            signals.append("白穿黄线买")
        
        if signals:
            print(f"触发信号: {', '.join(signals)}")
        else:
            print("无买入信号")
        
        print(f"\n=== A、B线 ===")
        print(f"A线 (80*N3): {result['A']:.0f}")
        print(f"B线 (20*N3): {result['B']:.0f}")
        
        return result
        
    except Exception as e:
        print(f"计算指标时出错: {e}")
        return None


def batch_calculate(stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
    """
    批量计算指定时间段的指标
    
    Args:
        stock_code: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        包含所有指标的DataFrame
    """
    try:
        df = load_stock_data(stock_code)
        indicators = calculate_tonghuashun_indicators(df)
        
        # 筛选日期范围
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        mask = (df['date'] >= start_dt) & (df['date'] <= end_dt)
        
        result_df = df[mask][['date', 'open', 'close', 'high', 'low', 'volume']].copy()
        
        # 添加指标列
        for name, series in indicators.items():
            result_df[name] = series[mask]
            
        return result_df
        
    except Exception as e:
        print(f"批量计算时出错: {e}")
        return None


def validate_implementation():
    """验证实现的准确性"""
    print("=== 验证同花顺指标实现 ===")
    print("使用准确参数: N1=3, N2=21, N3=1")
    
    stock_code = "002203"
    
    # 验证数据
    validation_data = [
        ("2024-07-16", {"短期": 6.67, "长期": 85.11, "A": 80, "B": 20}),
        ("2024-07-15", {"短期": 55.56, "长期": 91.49, "A": 80, "B": 20}),
    ]
    
    for date, expected in validation_data:
        print(f"\n--- 验证日期: {date} ---")
        result = display_indicators(stock_code, date)
        
        if result:
            print(f"\n验证结果:")
            for key, expected_value in expected.items():
                actual_value = result.get(key, 0)
                diff = abs(actual_value - expected_value)
                tolerance = expected_value * 0.05  # 5% 容差
                
                status = "✓" if diff <= tolerance else "⚠"
                print(f"  {status} {key}: 实际={actual_value:.2f}, 期望={expected_value:.2f}, 差异={diff:.2f}")


if __name__ == "__main__":
    validate_implementation()
    
    # 显示最近几天的趋势
    print("\n" + "="*60)
    print("=== 最近一周指标趋势 ===")
    recent_data = batch_calculate("002203", "2024-07-10", "2024-07-20")
    if recent_data is not None:
        print(recent_data[['date', 'close', '短期', '长期']].to_string(index=False))
