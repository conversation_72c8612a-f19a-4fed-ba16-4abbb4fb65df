"""
测试脚本 - 验证指标计算的正确性
"""

from main import display_indicators, batch_calculate, find_buy_signals
import pandas as pd


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    stock_code = "002203"
    test_date = "2024-07-16"
    
    try:
        result = display_indicators(stock_code, test_date)
        if result:
            print("✓ 基本指标计算功能正常")
        else:
            print("✗ 基本指标计算失败")
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")


def test_batch_calculation():
    """测试批量计算功能"""
    print("\n=== 测试批量计算功能 ===")
    
    stock_code = "002203"
    start_date = "2024-07-10"
    end_date = "2024-07-20"
    
    try:
        df = batch_calculate(stock_code, start_date, end_date)
        if df is not None and len(df) > 0:
            print(f"✓ 批量计算成功，获得 {len(df)} 条记录")
            print(f"  日期范围: {df['date'].min()} 到 {df['date'].max()}")
            print(f"  包含列: {list(df.columns)}")
        else:
            print("✗ 批量计算失败或无数据")
    except Exception as e:
        print(f"✗ 批量计算测试失败: {e}")


def test_buy_signals():
    """测试买入信号功能"""
    print("\n=== 测试买入信号功能 ===")
    
    stock_code = "002203"
    start_date = "2024-06-01"
    end_date = "2024-07-31"
    
    try:
        signals = find_buy_signals(stock_code, start_date, end_date)
        if signals is not None:
            print(f"✓ 买入信号检测功能正常")
            if len(signals) > 0:
                print(f"  发现 {len(signals)} 个买入信号")
            else:
                print("  在指定时间段内未发现买入信号")
        else:
            print("✗ 买入信号检测失败")
    except Exception as e:
        print(f"✗ 买入信号测试失败: {e}")


def test_parameter_variations():
    """测试不同参数组合"""
    print("\n=== 测试不同参数组合 ===")
    
    stock_code = "002203"
    test_date = "2024-07-16"
    
    parameter_sets = [
        (5, 60, 1),   # 传统参数
        (9, 34, 1),   # 默认参数
        (13, 21, 1),  # 斐波那契数列
        (10, 50, 2),  # 自定义参数
    ]
    
    for n1, n2, n3 in parameter_sets:
        try:
            print(f"\n--- 参数组合: N1={n1}, N2={n2}, N3={n3} ---")
            result = display_indicators(stock_code, test_date, n1, n2, n3)
            if result:
                print(f"✓ 参数组合 ({n1}, {n2}, {n3}) 计算成功")
            else:
                print(f"✗ 参数组合 ({n1}, {n2}, {n3}) 计算失败")
        except Exception as e:
            print(f"✗ 参数组合 ({n1}, {n2}, {n3}) 测试失败: {e}")


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 测试边界情况 ===")
    
    stock_code = "002203"
    
    # 测试数据开始日期附近
    early_dates = ["2022-04-14", "2022-04-15", "2022-04-18"]
    
    for date in early_dates:
        try:
            result = display_indicators(stock_code, date, n1=5, n2=10)  # 使用较小的参数
            if result:
                print(f"✓ 早期日期 {date} 计算成功")
            else:
                print(f"✗ 早期日期 {date} 计算失败")
        except Exception as e:
            print(f"✗ 早期日期 {date} 测试失败: {e}")


def performance_test():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    import time
    
    stock_code = "002203"
    start_date = "2024-01-01"
    end_date = "2024-07-31"
    
    try:
        start_time = time.time()
        df = batch_calculate(stock_code, start_date, end_date)
        end_time = time.time()
        
        if df is not None:
            duration = end_time - start_time
            records = len(df)
            print(f"✓ 性能测试完成")
            print(f"  处理记录数: {records}")
            print(f"  耗时: {duration:.3f} 秒")
            print(f"  平均每条记录: {duration/records*1000:.2f} 毫秒")
        else:
            print("✗ 性能测试失败")
    except Exception as e:
        print(f"✗ 性能测试失败: {e}")


def validation_test():
    """验证测试 - 与计划中的验证数据对比"""
    print("\n=== 验证测试 ===")
    
    stock_code = "002203"
    
    # 验证数据
    validation_data = [
        ("2024-07-16", {"短期": 6.67, "长期": 85.11, "A": 80, "B": 20}),
        ("2024-07-15", {"短期": 55.56, "长期": 91.49, "A": 80, "B": 20}),
    ]
    
    for date, expected in validation_data:
        try:
            result = display_indicators(stock_code, date)
            if result:
                print(f"\n--- 验证日期: {date} ---")
                for key, expected_value in expected.items():
                    actual_value = result.get(key, 0)
                    diff = abs(actual_value - expected_value)
                    tolerance = expected_value * 0.1  # 10% 容差
                    
                    if diff <= tolerance:
                        print(f"✓ {key}: 实际={actual_value:.2f}, 期望={expected_value:.2f}, 差异={diff:.2f}")
                    else:
                        print(f"⚠ {key}: 实际={actual_value:.2f}, 期望={expected_value:.2f}, 差异={diff:.2f} (超出容差)")
            else:
                print(f"✗ 验证日期 {date} 计算失败")
        except Exception as e:
            print(f"✗ 验证日期 {date} 测试失败: {e}")


if __name__ == "__main__":
    print("开始运行测试套件...")
    print("=" * 60)
    
    test_basic_functionality()
    test_batch_calculation()
    test_buy_signals()
    test_parameter_variations()
    test_edge_cases()
    performance_test()
    validation_test()
    
    print("\n" + "=" * 60)
    print("测试套件运行完成！")
